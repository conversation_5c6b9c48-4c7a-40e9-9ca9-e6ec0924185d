const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

class ApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  private async handleResponse(response: Response) {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // User & Balance
  async getUserBalance() {
    const response = await fetch(`${API_URL}/api/users/balance`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getUserTransactions() {
    const response = await fetch(`${API_URL}/api/users/transactions`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getTransaction(transactionId: string) {
    const response = await fetch(`${API_URL}/api/transactions/${transactionId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getTransactionStats() {
    const response = await fetch(`${API_URL}/api/transactions/stats/summary`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Payments & Transfers
  async sendPayment(recipientId: string, amount: number, description?: string, currency = 'LZAR') {
    const response = await fetch(`${API_URL}/api/payments/transfer`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        recipientId,
        amount,
        description,
        currency,
      }),
    });
    return this.handleResponse(response);
  }

  async sendBatchPayment(transfers: Array<{
    recipientId: string;
    amount: number;
    description?: string;
    currency?: string;
  }>) {
    const response = await fetch(`${API_URL}/api/payments/batch-transfer`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ transfers }),
    });
    return this.handleResponse(response);
  }

  async getRecipient(recipientId: string) {
    const response = await fetch(`${API_URL}/api/payments/recipient/${recipientId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Charges
  async createCharge(amount: number, description?: string, currency = 'LZAR') {
    const response = await fetch(`${API_URL}/api/users/charges`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({
        amount,
        description,
        currency,
      }),
    });
    return this.handleResponse(response);
  }

  async getUserCharges() {
    const response = await fetch(`${API_URL}/api/users/charges`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async getCharge(chargeId: string) {
    const response = await fetch(`${API_URL}/api/payments/charge/${chargeId}`, {
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  async payCharge(chargeId: string) {
    const response = await fetch(`${API_URL}/api/payments/pay-charge/${chargeId}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Redemption
  async redeemTokens(amount: number) {
    const response = await fetch(`${API_URL}/api/users/redeem`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ amount }),
    });
    return this.handleResponse(response);
  }

  // Activate payment functionality
  async activatePayment() {
    const response = await fetch(`${API_URL}/api/users/activate-pay`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });
    return this.handleResponse(response);
  }

  // Add funds to user account
  async addFunds(amount: number, currency = 'LZAR') {
    const response = await fetch(`${API_URL}/api/users/add-funds`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ amount, currency }),
    });
    return this.handleResponse(response);
  }
}

export const apiService = new ApiService();
