# ZAR Stablecoin Payment App

A Progressive Web App (PWA) for ZAR Stablecoin payments, transfers, and wallet management built with Next.js and Express.js.

## Features

- 🔐 **Secure Authentication** - JWT-based authentication with bcrypt password hashing
- 💰 **Wallet Management** - View balances, transaction history, and account details
- 💸 **Send & Receive Payments** - Transfer LZAR tokens between users
- 📱 **Progressive Web App** - Installable on mobile and desktop devices
- 🔄 **Real-time Updates** - Live balance and transaction updates
- 📊 **Transaction History** - Detailed payment history with filtering
- 🎯 **Payment Requests** - Create and manage payment charges
- 🔒 **Security Features** - Rate limiting, CORS protection, and input validation

## Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Heroicons** - Beautiful SVG icons
- **next-pwa** - PWA functionality

### Backend
- **Express.js 4** - Node.js web framework
- **JWT** - JSON Web Token authentication
- **bcryptjs** - Password hashing
- **Helmet** - Security middleware
- **CORS** - Cross-origin resource sharing
- **Rate Limiting** - API protection

## Project Structure

```
ZARStablecoinPaymentApp/
├── backend/                 # Express.js API server
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Authentication & validation
│   │   ├── routes/          # API routes
│   │   ├── services/        # External API integration
│   │   └── server.js        # Main server file
│   ├── .env                 # Environment variables
│   └── package.json
├── frontend/                # Next.js PWA
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # React components
│   │   ├── contexts/        # React contexts
│   │   └── services/        # API service layer
│   ├── public/
│   │   ├── icons/           # PWA icons
│   │   └── manifest.json    # PWA manifest
│   ├── .env.local           # Environment variables
│   └── package.json
└── README.md
```

## Getting Started

### Prerequisites

- Node.js 18+ and npm
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ZARStablecoinPaymentApp
   ```

2. **Install backend dependencies**
   ```bash
   cd backend
   npm install
   ```

3. **Install frontend dependencies**
   ```bash
   cd ../frontend
   npm install
   ```

4. **Configure environment variables**
   
   Backend (`.env`):
   ```env
   PORT=5000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:3000
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   JWT_EXPIRES_IN=24h
   RAPYD_API_BASE_URL=https://seal-app-qp9cc.ondigitalocean.app/api/v1
   RAPYD_API_TOKEN=QUxMIFlPVVIgQkFTRSBBUkUgQkVMT05HIFRPIFVT
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   BCRYPT_ROUNDS=12
   ```
   
   Frontend (`.env.local`):
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:5000
   ```

### Development

1. **Start the backend server**
   ```bash
   cd backend
   npm run dev
   ```
   The API will be available at `http://localhost:5000`

2. **Start the frontend development server**
   ```bash
   cd frontend
   npm run dev
   ```
   The app will be available at `http://localhost:3000`

### Building for Production

1. **Build the frontend**
   ```bash
   cd frontend
   npm run build
   ```

2. **Start production servers**
   ```bash
   # Backend
   cd backend
   npm start
   
   # Frontend (in another terminal)
   cd frontend
   npm start
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/refresh` - Refresh JWT token

### User Management
- `GET /api/users/balance` - Get user balance
- `GET /api/users/transactions` - Get user transactions
- `POST /api/users/charges` - Create payment charge
- `POST /api/users/redeem` - Redeem LZAR tokens

### Payments
- `POST /api/payments/transfer` - Send payment
- `POST /api/payments/batch-transfer` - Send batch payments
- `GET /api/payments/recipient/:id` - Get recipient info
- `GET /api/payments/charge/:id` - Get charge details

### Transactions
- `GET /api/transactions` - Get transaction history
- `GET /api/transactions/:id` - Get specific transaction
- `GET /api/transactions/stats/summary` - Get transaction statistics

## PWA Features

- **Installable** - Can be installed on mobile and desktop
- **Offline Ready** - Basic offline functionality with service worker
- **Responsive Design** - Works on all screen sizes
- **App-like Experience** - Native app feel and behavior

## Security Features

- JWT authentication with secure token storage
- Password hashing with bcrypt
- Rate limiting to prevent abuse
- CORS protection
- Input validation and sanitization
- Helmet.js security headers

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the ISC License.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
