const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const rapydService = require('../services/rapydService');
const { userBalances } = require('./admin');

const router = express.Router();

// In-memory storage for user transactions
const userTransactions = new Map();

// Helper function to add transaction to local storage
const addTransaction = (userId, transaction) => {
  if (!userTransactions.has(userId)) {
    userTransactions.set(userId, []);
  }
  const transactions = userTransactions.get(userId);
  transactions.unshift(transaction); // Add to beginning for newest first
  userTransactions.set(userId, transactions);
};

// Get user balance
router.get('/balance', authenticateToken, async (req, res) => {
  try {
    const balance = await rapydService.getUserBalance(req.user.rapydUserId);
    console.log('Successfully fetched balance from Rapyd API');
    res.json(balance);
  } catch (error) {
    console.log('Rapyd API error, using demo balance:', error.message);
    // Return balance from demo storage or default
    const userBalance = userBalances.get(req.user.email) || {
      available: 1000.50,
      pending: 25.00,
      currency: 'LZAR'
    };
    res.json(userBalance);
  }
});

// Get user transactions
router.get('/transactions', authenticateToken, async (req, res) => {
  // Always check local transactions first
  const localTransactions = userTransactions.get(req.user.email) || [];

  try {
    const rapydTransactions = await rapydService.getUserTransactions(req.user.rapydUserId);
    console.log('Successfully fetched transactions from Rapyd API');

    // Combine Rapyd transactions with local transactions
    const allTransactions = [...localTransactions, ...(rapydTransactions.transactions || rapydTransactions || [])];

    // Sort by creation date (newest first)
    allTransactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json(allTransactions);
  } catch (error) {
    console.log('Rapyd API error, using local transactions:', error.message);

    // If no local transactions, return demo data
    if (localTransactions.length === 0) {
      res.json([
        {
          id: 'tx_demo_1',
          type: 'received',
          amount: 500.00,
          currency: 'LZAR',
          status: 'completed',
          description: 'Welcome bonus',
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          senderId: 'system'
        }
      ]);
    } else {
      res.json(localTransactions);
    }
  }
});

// Get specific transaction
router.get('/transactions/:transactionId', authenticateToken, async (req, res) => {
  try {
    const { transactionId } = req.params;
    const transaction = await rapydService.getTransaction(req.user.rapydUserId, transactionId);
    res.json(transaction);
  } catch (error) {
    console.error('Get transaction error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to get transaction',
      message: error.message
    });
  }
});

// Activate payment functionality for user
router.post('/activate-pay', authenticateToken, async (req, res) => {
  try {
    const result = await rapydService.activatePay(req.user.rapydUserId);
    res.json(result);
  } catch (error) {
    console.error('Activate pay error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to activate payment',
      message: error.message
    });
  }
});

// Get user charges
router.get('/charges', authenticateToken, async (req, res) => {
  try {
    const charges = await rapydService.getUserCharges(req.user.rapydUserId);
    res.json(charges);
  } catch (error) {
    console.error('Get charges error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to get charges',
      message: error.message
    });
  }
});

// Create a new charge
router.post('/charges', [
  authenticateToken,
  body('amount').isNumeric().isFloat({ min: 0.01 }),
  body('description').optional().trim(),
  body('currency').optional().isIn(['ZAR', 'LZAR']).default('LZAR')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { amount, description, currency } = req.body;
    
    const chargeData = {
      amount,
      description: description || `Payment request for ${amount} ${currency}`,
      currency: currency || 'LZAR'
    };

    try {
      const charge = await rapydService.createCharge(req.user.rapydUserId, chargeData);
      res.status(201).json(charge);
    } catch (apiError) {
      console.log('Using mock charge for demo:', apiError.message);
      // Return mock charge response for demo
      res.status(201).json({
        id: `charge_${Date.now()}`,
        amount: amount,
        currency: currency,
        description: description,
        status: 'pending',
        userId: req.user.rapydUserId,
        createdAt: new Date().toISOString(),
        paymentUrl: `${req.protocol}://${req.get('host')}/pay/charge_${Date.now()}`
      });
    }
  } catch (error) {
    console.error('Create charge error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to create charge',
      message: error.message
    });
  }
});

// Redeem LZAR tokens
router.post('/redeem', [
  authenticateToken,
  body('amount').isNumeric().isFloat({ min: 0.01 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { amount } = req.body;

    const result = await rapydService.redeemLZAR(req.user.rapydUserId, amount);
    res.json(result);
  } catch (error) {
    console.error('Redeem error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to redeem tokens',
      message: error.message
    });
  }
});

// Add funds to user account (for demo purposes)
router.post('/add-funds', [
  authenticateToken,
  body('amount').isNumeric().isFloat({ min: 0.01 }),
  body('currency').optional().isIn(['ZAR', 'LZAR']).default('LZAR')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { amount, currency = 'LZAR' } = req.body;

    // In a real system, this would integrate with payment processors
    // For demo, we'll simulate adding funds
    try {
      const result = await rapydService.mintTokens(amount, `Funds added for user ${req.user.email}`, req.user.rapydUserId);
      res.json(result);
    } catch (apiError) {
      console.log('Using mock add funds for demo:', apiError.message);
      // Return mock success response for demo
      res.json({
        transactionId: `fund_${Date.now()}`,
        status: 'completed',
        amount: amount,
        currency: currency,
        type: 'deposit',
        description: `Added ${amount} ${currency} to account`,
        createdAt: new Date().toISOString(),
        message: 'Funds added successfully (demo mode)'
      });
    }
  } catch (error) {
    console.error('Add funds error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to add funds',
      message: error.message
    });
  }
});

module.exports = router;
