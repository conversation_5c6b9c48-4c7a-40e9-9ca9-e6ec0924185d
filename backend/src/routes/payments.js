const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const rapydService = require('../services/rapydService');
const { userBalances } = require('./admin');

const router = express.Router();

// In-memory storage for user transactions (shared with users.js)
const userTransactions = new Map();

// Send payment to another user
router.post('/transfer', [
  authenticateToken,
  body('recipientId').notEmpty().trim(),
  body('amount').isNumeric().isFloat({ min: 0.01 }),
  body('description').optional().trim(),
  body('currency').optional().isIn(['ZAR', 'LZAR']).default('LZAR')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { recipientId, amount, description, currency } = req.body;
    
    const transferData = {
      recipientId,
      amount,
      description: description || `Transfer of ${amount} ${currency}`,
      currency: currency || 'LZAR'
    };

    try {
      const result = await rapydService.transfer(req.user.rapydUserId, transferData);
      console.log('Successfully completed transfer via Rapyd API');

      // Store transaction in user's history
      const transaction = {
        id: result.transactionId || `tx_${Date.now()}`,
        type: 'sent',
        amount: amount,
        currency: currency,
        status: result.status || 'completed',
        description: description,
        createdAt: new Date().toISOString(),
        recipientId: recipientId
      };

      // Add to sender's transaction history
      const senderTransactions = userTransactions.get(req.user.email) || [];
      senderTransactions.unshift(transaction);
      userTransactions.set(req.user.email, senderTransactions);
      console.log(`Stored transaction for ${req.user.email}:`, transaction.id, `Total transactions: ${senderTransactions.length}`);

      res.json(result);
    } catch (apiError) {
      console.log('Rapyd API error, using demo transfer:', apiError.message);

      // Create transaction record for demo mode
      const transactionId = `tx_${Date.now()}`;
      const transaction = {
        id: transactionId,
        type: 'sent',
        amount: amount,
        currency: currency,
        status: 'completed',
        description: description,
        createdAt: new Date().toISOString(),
        recipientId: recipientId
      };

      // Add to sender's transaction history
      const senderTransactions = userTransactions.get(req.user.email) || [];
      senderTransactions.unshift(transaction);
      userTransactions.set(req.user.email, senderTransactions);
      console.log(`Stored demo transaction for ${req.user.email}:`, transaction.id, `Total transactions: ${senderTransactions.length}`);

      // Return mock success response for demo
      res.json({
        transactionId: transactionId,
        status: 'completed',
        amount: amount,
        currency: currency,
        recipientId: recipientId,
        description: description,
        createdAt: new Date().toISOString(),
        message: 'Transfer completed successfully (demo mode)'
      });
    }
  } catch (error) {
    console.error('Transfer error:', error);
    res.status(error.status || 500).json({
      error: 'Transfer failed',
      message: error.message
    });
  }
});

// Batch transfer to multiple recipients
router.post('/batch-transfer', [
  authenticateToken,
  body('transfers').isArray({ min: 1 }),
  body('transfers.*.recipientId').notEmpty().trim(),
  body('transfers.*.amount').isNumeric().isFloat({ min: 0.01 }),
  body('transfers.*.description').optional().trim(),
  body('transfers.*.currency').optional().isIn(['ZAR', 'LZAR']).default('LZAR')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { transfers } = req.body;
    
    // Add default values and validate each transfer
    const processedTransfers = transfers.map(transfer => ({
      ...transfer,
      description: transfer.description || `Transfer of ${transfer.amount} ${transfer.currency || 'LZAR'}`,
      currency: transfer.currency || 'LZAR'
    }));

    const result = await rapydService.batchTransfer(req.user.rapydUserId, { transfers: processedTransfers });
    res.json(result);
  } catch (error) {
    console.error('Batch transfer error:', error);
    res.status(error.status || 500).json({
      error: 'Batch transfer failed',
      message: error.message
    });
  }
});

// Get recipient information
router.get('/recipient/:recipientId', authenticateToken, async (req, res) => {
  try {
    const { recipientId } = req.params;
    const recipient = await rapydService.getRecipient(recipientId);
    res.json(recipient);
  } catch (error) {
    console.error('Get recipient error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to get recipient information',
      message: error.message
    });
  }
});

// Get charge details
router.get('/charge/:chargeId', authenticateToken, async (req, res) => {
  try {
    const { chargeId } = req.params;
    const charge = await rapydService.getCharge(chargeId);
    res.json(charge);
  } catch (error) {
    console.error('Get charge error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to get charge details',
      message: error.message
    });
  }
});

// Pay a charge
router.post('/pay-charge/:chargeId', [
  authenticateToken,
  body('paymentMethod').optional().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { chargeId } = req.params;
    const { paymentMethod } = req.body;

    // Get charge details first
    const charge = await rapydService.getCharge(chargeId);
    
    if (!charge) {
      return res.status(404).json({ error: 'Charge not found' });
    }

    // Create transfer to pay the charge
    const transferData = {
      recipientId: charge.userId, // Assuming charge has userId field
      amount: charge.amount,
      description: `Payment for charge ${chargeId}`,
      currency: charge.currency || 'LZAR',
      chargeId: chargeId
    };

    const result = await rapydService.transfer(req.user.rapydUserId, transferData);
    res.json({
      ...result,
      chargeId: chargeId,
      message: 'Charge paid successfully'
    });
  } catch (error) {
    console.error('Pay charge error:', error);
    res.status(error.status || 500).json({
      error: 'Failed to pay charge',
      message: error.message
    });
  }
});

module.exports = router;
