const axios = require('axios');

class RapydService {
  constructor() {
    this.baseURL = process.env.RAPYD_API_BASE_URL;
    this.apiToken = process.env.RAPYD_API_TOKEN;
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json'
      }
    });
  }

  // Token Management
  async createApiToken(description = '') {
    try {
      const response = await this.client.post('/tokens', { description });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async listApiTokens() {
    try {
      const response = await this.client.get('/tokens');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async revokeApiToken(tokenId) {
    try {
      const response = await this.client.post('/tokens/revoke', { id: tokenId });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // User Management
  async createUser(userData) {
    try {
      // Split name into firstName and lastName for Rapyd API
      const nameParts = userData.name.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      const rapydUserData = {
        firstName,
        lastName,
        email: userData.email,
        phone: userData.phone || ''
      };

      const response = await this.client.post('/users', rapydUserData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUsers() {
    try {
      const response = await this.client.get('/users');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUserById(userId) {
    try {
      const response = await this.client.get(`/users/${userId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateUser(userId, userData) {
    try {
      const response = await this.client.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Business Management
  async getFloat() {
    try {
      const response = await this.client.get('/float');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async mintTokens(amount, notes = '', referralId = '') {
    try {
      const response = await this.client.post('/mint', {
        amount,
        notes,
        referralId
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async enableGas(userId, amount) {
    try {
      const response = await this.client.post('/enable-gas', {
        userId,
        amount
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Transfers
  async activatePay(userId) {
    try {
      const response = await this.client.post(`/activate-pay/${userId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getRecipient(recipientId) {
    try {
      const response = await this.client.get(`/recipient/${recipientId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async transfer(userId, transferData) {
    try {
      const response = await this.client.post(`/transfer/${userId}`, transferData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async batchTransfer(userId, transfersData) {
    try {
      const response = await this.client.post(`/transfer/batch/${userId}`, transfersData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Transactions
  async getUserBalance(userId) {
    try {
      const response = await this.client.get(`/${userId}/balance`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUserTransactions(userId) {
    try {
      const response = await this.client.get(`/${userId}/transactions`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getTransaction(userId, transactionId) {
    try {
      const response = await this.client.get(`/${userId}/transactions/${transactionId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Charges
  async createCharge(userId, chargeData) {
    try {
      const response = await this.client.post(`/charge/${userId}/create`, chargeData);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getUserCharges(userId) {
    try {
      const response = await this.client.get(`/charge/${userId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getCharge(chargeId) {
    try {
      const response = await this.client.get(`/retrieve-charge/${chargeId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Redemption
  async redeemLZAR(userId, amount) {
    try {
      const response = await this.client.post('/redeem', {
        userId,
        amount
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Get pending transactions
  async getPendingTransactions() {
    try {
      const response = await this.client.get('/transactions/pending');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handling
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      return {
        status: error.response.status,
        message: error.response.data?.message || error.response.data?.error || 'API Error',
        data: error.response.data
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        status: 500,
        message: 'Network error - no response from server',
        data: null
      };
    } else {
      // Something else happened
      return {
        status: 500,
        message: error.message || 'Unknown error occurred',
        data: null
      };
    }
  }
}

module.exports = new RapydService();
